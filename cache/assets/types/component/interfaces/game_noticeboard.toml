id=1701
name="game_noticeboard"

# Universe
[[component]]
name="universe"
type="layer"
width=190
height=261
onload=[31, "component:scroll_bar", "component:scroll_layer", 792, 789, 790, 791, 773, 788]

# Main container
[[component]]
name="main_container"
type="layer"
layer="universe"
x=3
y=26
width=184
height=229

# Layout
[[component]]
name="layout"
type="layer"
layer="universe"
x=3
y=26
width=184
height=229
onload=[712, "component:self", 0]

# Background
[[component]]
type="rectangle"
layer="main_container"
widthmode=1
heightmode=1
color="170801"
opacity=166
filled=true

# Title header
[[component]]
type="text"
layer="universe"
text="Dashboard"
font=496
xallignment=1
yalllignment=1
y=4
widthmode=1
height=18

# Scroll layer
[[component]]
name="scroll_layer"
type="layer"
layer="main_container"
x=5
y=5
width=157
height=12
heightmode=1
scrollheight=522

# Scroll bar
[[component]]
name="scroll_bar"
type="layer"
layer="main_container"
x=162
y=5
width=16
height=218



# Server Information
[[component]]
type="text"
layer="scroll_layer"
text="Server Information"
font=496
x=2
y=2
widthmode=1
height=10

# Players online
[[component]]
name="players_online"
type="text"
layer="scroll_layer"
text="Players Online:"
font=494
op1=""
x=2
y=22
widthmode=1
height=10

# Staff online
[[component]]
name="staff_online"
type="text"
layer="scroll_layer"
text="Staff Online:"
font=494
op1="<col=ff981f>View</col>"
opbase="Online staff"
clickmask=2
x=2
y=36
widthmode=1
height=10
onmouseover=[45, "component:self", "color:ffffff"]
onmouseleave=[45, "component:self", "color:ff981f"]
onvartransmit=[3502, "component:players_online", "component:self"]
vartransmittriggers=[3502, 3508]

# Uptime
[[component]]
type="text"
layer="scroll_layer"
text="Uptime:"
font=494
x=2
y=50
widthmode=1
height=10
onload=[3500, "component:self", "component:personal_bonus_xp", "component:global_bonus_xp", 1]

# Time
[[component]]
type="text"
layer="scroll_layer"
text="Time:"
font=494
x=2
y=64
widthmode=1
height=10




# Player Information
[[component]]
type="text"
layer="scroll_layer"
text="Player Information"
font=496
x=2
y=96
widthmode=1
height=10

# XP mode
[[component]]
type="text"
layer="scroll_layer"
text="XP Rate:"
font=494
x=2
y=116
widthmode=1
height=10

# Game mode
[[component]]
type="text"
layer="scroll_layer"
text="Mode:"
font=494
x=2
y=130
widthmode=1
height=10

# Total donated
[[component]]
type="text"
layer="scroll_layer"
text="Total donated:"
font=494
x=2
y=144
widthmode=1
height=10

# Drop Rate
[[component]]
type="text"
layer="scroll_layer"
text="Drop Rate:"
font=494
x=2
y=158
widthmode=1
height=10

# Loyalty Points
[[component]]
type="text"
layer="scroll_layer"
text="Loyalty Points:"
font=494
x=2
y=172
widthmode=1
height=10

# Donor Points
[[component]]
type="text"
layer="scroll_layer"
text="Donor Points:"
font=494
x=2
y=186
widthmode=1
height=10

# Vote Points
[[component]]
type="text"
layer="scroll_layer"
text="Vote Points:"
font=494
x=2
y=200
widthmode=1
height=10


# Tools
[[component]]
type="text"
layer="scroll_layer"
text="Tools"
font=496
x=2
y=226
widthmode=1
height=10

# Drop viewer icon
[[component]]
type="graphic"
layer="scroll_layer"
sprite=1113
y=243
width=18
height=16

# Drop Viewer
[[component]]
type="text"
layer="scroll_layer"
text="Drop Viewer"
opbase="Drop Viewer"
op1="<col=ff981f>Open</col>"
clickmask=2
font=494
x=17
y=246
widthmode=1
height=10
onmouseover=[45, "component:self", "color:ffffff"]
onmouseleave=[45, "component:self", "color:ff981f"]

# Daily challenges icon
[[component]]
type="graphic"
layer="scroll_layer"
sprite=10001
x=2
y=261
width=13
height=13

# Daily Challenges
[[component]]
type="text"
layer="scroll_layer"
text="Daily Challenges"
opbase="Daily Challenges"
op1="<col=ff981f>Open</col>"
clickmask=2
font=494
x=17
y=262
widthmode=1
height=10
onmouseover=[45, "component:self", "color:ffffff"]
onmouseleave=[45, "component:self", "color:ff981f"]

# Donator Toggles icon
[[component]]
type="graphic"
layer="scroll_layer"
sprite=2264
x=2
y=277
width=13
height=13

# Donator Toggles
[[component]]
type="text"
layer="scroll_layer"
text="Donator Toggles"
opbase="Donator Toggles"
op1="<col=ff981f>Open</col>"
clickmask=2
font=494
x=17
y=278
widthmode=1
height=10
onmouseover=[45, "component:self", "color:ffffff"]
onmouseleave=[45, "component:self", "color:ff981f"]





# Boosts
[[component]]
type="text"
layer="scroll_layer"
text="Boosts"
font=496
x=2
y=304
widthmode=1
height=10

# Global Bonus XP
[[component]]
name="global_bonus_xp"
type="text"
layer="scroll_layer"
text="Global BXP:"
tooltip="Global 50% Bonus experience boost<br>Does not stack with the private boost."
font=494
x=2
y=324
widthmode=1
height=10

# Bonus XP
[[component]]
name="personal_bonus_xp"
type="text"
layer="scroll_layer"
text="Personal BXP:"
tooltip="Private 50% Bonus experience boost<br>Does not stack with the global boost."
font=494
x=2
y=338
widthmode=1
height=10

# Check Boosters
[[component]]
type="text"
layer="scroll_layer"
text="Check Boosters"
opbase="Check Boosters"
op1="<col=ff981f>Check</col>"
clickmask=2
font=494
x=1
y=352
widthmode=1
height=10
onmouseover=[45, "component:self", "color:ffffff"]
onmouseleave=[45, "component:self", "color:ff981f"]





[[component]]
type="text"
layer="scroll_layer"
text="Useful Links"
font=496
x=2
y=384
widthmode=1
height=10

# Website icon
[[component]]
type="graphic"
layer="scroll_layer"
sprite=10002
x=2
y=402
width=13
height=13

# Website
[[component]]
type="text"
layer="scroll_layer"
text="Website"
opbase="Website"
op1="<col=ff981f>Visit</col>"
clickmask=2
font=494
x=17
y=404
widthmode=1
height=10
onmouseover=[45, "component:self", "color:ffffff"]
onmouseleave=[45, "component:self", "color:ff981f"]

# Discord icon
[[component]]
type="graphic"
layer="scroll_layer"
sprite=10004
x=2
y=416
width=13
height=13

# Discord
[[component]]
type="text"
layer="scroll_layer"
text="Discord"
opbase="Discord"
op1="<col=ff981f>Visit</col>"
clickmask=2
font=494
x=17
y=418
widthmode=1
height=10
onmouseover=[45, "component:self", "color:ffffff"]
onmouseleave=[45, "component:self", "color:ff981f"]

# Store icon
[[component]]
type="graphic"
layer="scroll_layer"
sprite=10005
x=2
y=430
width=13
height=13

# Store
[[component]]
type="text"
layer="scroll_layer"
text="Store"
opbase="Store"
op1="<col=ff981f>Open</col>"
clickmask=2
font=494
x=17
y=432
widthmode=1
height=10
onmouseover=[45, "component:self", "color:ffffff"]
onmouseleave=[45, "component:self", "color:ff981f"]

# Tooltip
[[component]]
name="tooltip"
type="layer"
layer="universe"
width=1
height=1