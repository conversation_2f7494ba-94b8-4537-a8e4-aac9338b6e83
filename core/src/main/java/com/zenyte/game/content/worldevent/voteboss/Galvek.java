package com.zenyte.game.content.worldevent.voteboss;

import com.zenyte.game.task.WorldTask;
import com.zenyte.game.task.WorldTasksManager;
import com.zenyte.game.util.Direction;
import com.zenyte.game.world.Projectile;
import com.zenyte.game.world.World;
import com.zenyte.game.world.broadcasts.BroadcastType;
import com.zenyte.game.world.broadcasts.WorldBroadcasts;
import com.zenyte.game.world.entity.Entity;
import com.zenyte.game.world.entity.EntityHitBar;
import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.entity.masks.Animation;
import com.zenyte.game.world.entity.masks.Hit;
import com.zenyte.game.world.entity.npc.NPC;
import com.zenyte.game.world.entity.npc.combat.CombatScript;
import com.zenyte.game.world.entity.npc.combatdefs.AttackType;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.region.CharacterLoop;
import com.zenyte.game.world.region.GlobalAreaManager;
import com.zenyte.game.world.region.RSPolygon;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

import static com.zenyte.game.content.worldevent.voteboss.GalvekConstants.*;

/**
 * <AUTHOR>
 */
public class Galvek extends NPC implements CombatScript {

    private static final Logger LOGGER = LoggerFactory.getLogger(Galvek.class);

    public static Location center = new Location(1631, 5724, 2);
    public static RSPolygon rsPolygon = RSPolygon.growAbsolute(center, 14);

    private int currentTransformation = GALVEK_NORMAL;
    private AttackType attackType = AttackType.MAGIC;
    private Animation attackAnimation = MAGIC_ANIM;
    private Projectile attackProjectile = MAGIC_PROJ;
    public boolean fightStarted = false;
    private int attackCounter;

    public Galvek() {
        super(GALVEK_SLEEPING, SPAWN_LOCATION, Direction.SOUTH, 0);
        this.attackDistance = 25;
        this.maxDistance = 25;
        var currentHp = this.getMaxHitpoints();
        var currentOnline = World.getOnlineActivePlayerCount();
        var modifiedHp = currentHp + (currentOnline * 50);
        this.setHitpoints(modifiedHp);
        hitBar = new EntityHitBar(this) {
            @Override
            public int getType() {
                return 21;
            }
        };
    }

    @Override
    public int getRespawnDelay() {
        return 1;
    }

    public void start() {
        WorldTasksManager.schedule(new WorldTask() {
            int ticks;
            @Override
            public void run() {
                switch (ticks) {
                    case 0:
                        setAnimation(DISAPPEAR_ANIM);
                        break;

                    case 1:
                        setTransformation(GALVEK_NORMAL);
                        setAnimation(LAND_ANIM);
                        break;

                    case 3:
                        setAnimation(CHANGE_ANIM);
                        break;

                    case 5:
                        setTransformation(GALVEK_MAGIC);
                        setAnimation(STAND_ANIM);
                        fightStarted = true;
                        stop();
                        break;
                }
                ticks++;
            }
        }, 1, 1);
    }

    @Override
    public void handleIngoingHit(final Hit hit) {
        super.handleIngoingHit(hit);
    }

    @Override
    protected void postHitProcess(Hit hit) {
        List<Entity> targets = getPossibleTargets(EntityType.PLAYER);
        for (Entity entity : targets) {
            if (entity instanceof final Player player) {
                if (!player.getHpHud().isOpen()) {
                    player.getHpHud().open(id, getMaxHitpoints());
                }
                player.getHpHud().updateValue(getHitpoints());
            }
        }
        super.postHitProcess(hit);
    }

    @Override
    protected boolean canMove(int fromX, int fromY, int direction) {
        return false;
    }

    @Override
    public boolean isWithinMeleeDistance(NPC npc, Entity target) {
        return true;
    }


    public void regularAttack(Player player) {
        if(player.getNextLocation() != null && player.getNextLocation().getRegionId() != this.getLocation().getRegionId())
            return;
        if (player.getLocation().getRegionId() == this.getLocation().getRegionId() && !player.isDying()) {
            final Location startTile = getFaceLocation(player);
            final int delay = World.sendProjectile(startTile.matches(player) ? getLocation() : startTile, player, attackProjectile);
            delayHit(this, delay, player, new Hit(this, getRandomMaxHit(this, getCombatDefinitions().getMaxHit(), getAttackType(), player), getAttackType().getHitType()));
        }
    }


    public void finishFight() {
        fightStarted = false;
        attackCounter = 0;
        attackType = AttackType.MAGIC;
        attackAnimation = MAGIC_ANIM;
        attackProjectile = MAGIC_PROJ;
        setTransformation(GALVEK_SLEEPING);
        for (int i = 0; i < 2; i++) {
            GalvekHandler.get().activateRandomInactiveWorldBoost();
        }
        WorldBroadcasts.sendMessage("<img=" + 85 + "><col=" + "e59400" + ">" + "<shad=000000>" + "Vote Boss: Galvek has been defeated! Only 50 more votes until Galvek returns!", BroadcastType.WORLD_BOSS, false);
        for(Player player: GlobalAreaManager.getArea(GalvekArea.class).getPlayers()) {
            if(player != null && player.getHpHud() != null) {
                player.getHpHud().close();
            }
        }
    }

    @Override
    protected void onDeath(Entity source) {
        finishFight();
        WorldTasksManager.schedule(new RemoveAllGalvekPlayersTask(), 0, 0);
        super.onDeath(source);
    }

    @Override
    protected void onFinish(@Nullable Entity source) {
        List<Entity> targets = getPossibleTargets(EntityType.PLAYER);
        for (Entity entity : targets) {
            if (entity instanceof final Player player) {
                player.getHpHud().close();
            }
        }
        super.onFinish(source);
    }

    @Override
    public boolean addWalkStep(int nextX, int nextY, int lastX, int lastY, boolean check) {
        return false;
    }

    @Override
    public boolean isForceAggressive() {
        return true;
    }

    @Override
    public boolean isEntityClipped() {
        return false;
    }

    @Override
    public boolean isTolerable() {
        return false;
    }

    @Override
    public boolean checkProjectileClip(Player player, boolean melee) {
        return false;
    }

    @Override
    public int attack(Entity target) {
        List<Player> players = CharacterLoop.find(getLocation(), 20, Player.class,
                (p) ->  p != null && !p.isDead() && !p.isDying() && !p.isFinished() && p.isInitialized());

        attackCounter++;

        setAnimation(attackAnimation);

        players.forEach(this::regularAttack);

        if(attackCounter >= 5) {
            switchAttack();
        }

        return 5;
    }

    @Override
    public boolean isAttackableNPC() {
        return fightStarted;
    }

    @Override
    protected void drop(Location tile) {
        if(!GalvekHandler.get().isEnabled()) {//no drops for anyone
            return;
        }
        super.drop(tile);
    }

    public AttackType getAttackType() {
        return attackType;
    };

    public void switchAttack() {
        attackCounter = 0;
        attackType = attackType == AttackType.MAGIC ? AttackType.RANGED : AttackType.MAGIC;
        attackAnimation = attackType == AttackType.MAGIC ? MAGIC_ANIM : RANGED_ANIM;
        attackProjectile = attackType == AttackType.MAGIC ? MAGIC_PROJ : RANGED_PROJ;
        currentTransformation = attackType == AttackType.MAGIC ? GALVEK_MAGIC : GALVEK_RANGED;

        delay(1, () -> setAnimation(CHANGE_ANIM));
        delay(3, () -> {
            setTransformation(currentTransformation);
        });
    }

    @Override
    public boolean canAttack(Player source) {
        return fightStarted;
    }


}
